#!/usr/bin/env python3
"""
修复数据库表缺失问题
"""

import sqlite3
import os
from datetime import datetime

def check_existing_tables():
    """检查现有表"""
    try:
        conn = sqlite3.connect('ticks.db')
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"现有表: {tables}")
        conn.close()
        return tables
    except Exception as e:
        print(f"检查表失败: {e}")
        return []

def create_missing_tables():
    """创建缺失的表"""
    try:
        conn = sqlite3.connect('ticks.db')
        cursor = conn.cursor()
        
        # 1. 创建 optimal_configs 表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS optimal_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            config_name TEXT NOT NULL,
            parameters TEXT NOT NULL,
            performance_metrics TEXT NOT NULL,
            backtest_period TEXT,
            optimization_method TEXT,
            market_characteristics TEXT,
            fitness_score REAL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            UNIQUE(symbol, config_name)
        )
        """)
        
        # 2. 创建 alert_rules 表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS alert_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            rule_name TEXT NOT NULL,
            condition_expr TEXT NOT NULL,
            channels TEXT NOT NULL,
            template TEXT NOT NULL,
            cooldown_seconds INTEGER DEFAULT 300,
            enabled BOOLEAN DEFAULT 1,
            priority INTEGER DEFAULT 1,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
        )
        """)
        
        # 3. 创建 alert_history 表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS alert_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            rule_name TEXT NOT NULL,
            rule_id INTEGER,
            message TEXT NOT NULL,
            alert_time TEXT NOT NULL,
            channels TEXT NOT NULL,
            success BOOLEAN DEFAULT 1,
            response_data TEXT,
            trigger_data TEXT
        )
        """)
        
        # 4. 创建 market_analysis 表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS market_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            analysis_date TEXT NOT NULL,
            analysis_period_days INTEGER NOT NULL,
            volatility_metrics TEXT NOT NULL,
            trend_metrics TEXT NOT NULL,
            liquidity_metrics TEXT NOT NULL,
            trading_patterns TEXT NOT NULL,
            recommendations TEXT,
            created_at TEXT NOT NULL,
            UNIQUE(symbol, analysis_date)
        )
        """)
        
        # 5. 创建 optimization_tasks 表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS optimization_tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            task_name TEXT NOT NULL,
            optimization_method TEXT NOT NULL,
            strategy_type TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            progress REAL DEFAULT 0.0,
            start_time TEXT,
            end_time TEXT,
            error_message TEXT,
            result_config_ids TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
        )
        """)
        
        # 创建索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_optimal_configs_symbol ON optimal_configs(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_optimal_configs_fitness ON optimal_configs(fitness_score DESC)",
            "CREATE INDEX IF NOT EXISTS idx_alert_rules_symbol ON alert_rules(symbol, enabled)",
            "CREATE INDEX IF NOT EXISTS idx_alert_history_symbol ON alert_history(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_alert_history_time ON alert_history(alert_time DESC)",
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        print("✅ 数据库表创建成功")
        
        # 插入默认的预警规则
        insert_default_alert_rules(cursor)
        
        # 插入示例优化配置
        insert_sample_optimal_configs(cursor)
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        print(f"创建表失败: {e}")
        import traceback
        traceback.print_exc()

def insert_default_alert_rules(cursor):
    """插入默认预警规则"""
    try:
        current_time = datetime.now().isoformat()
        
        default_rules = [
            (
                '159740', '买入信号预警', 'signal <= -0.006', 
                '["email"]', 
                '🔥 {symbol} 触发买入信号！\n当前价格: {price}\n信号强度: {signal:.4f}\n时间: {time}',
                300, 2, current_time, current_time
            ),
            (
                '159740', '强烈买入信号', 'signal <= -0.008',
                '["email", "wechat"]',
                '🚨 {symbol} 强烈买入信号！\n当前价格: {price}\n信号强度: {signal:.4f}\n建议立即关注！\n时间: {time}',
                180, 1, current_time, current_time
            ),
            (
                '159740', '止盈信号预警', 'position_profit_rate >= 0.005',
                '["email"]',
                '💰 {symbol} 达到止盈条件！\n当前收益率: {position_profit_rate:.2%}\n建议考虑止盈\n时间: {time}',
                600, 3, current_time, current_time
            ),
            (
                '159740', '风险预警', 'position_profit_rate <= -0.015',
                '["email", "wechat"]',
                '⚠️ {symbol} 风险预警！\n当前亏损: {position_profit_rate:.2%}\n请注意风险控制\n时间: {time}',
                300, 1, current_time, current_time
            )
        ]
        
        cursor.executemany("""
            INSERT OR IGNORE INTO alert_rules 
            (symbol, rule_name, condition_expr, channels, template, cooldown_seconds, priority, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, default_rules)
        
        print("✅ 默认预警规则插入成功")
        
    except Exception as e:
        print(f"插入默认预警规则失败: {e}")

def insert_sample_optimal_configs(cursor):
    """插入示例优化配置"""
    try:
        current_time = datetime.now().isoformat()
        
        sample_configs = [
            (
                '159740', '默认配置', 
                '{"buy_drop": -0.006, "profit_target": 0.005, "stop_loss": -0.015, "signal_window": 20}',
                '{"total_return": 0.08, "sharpe_ratio": 1.2, "max_drawdown": -0.05, "win_rate": 0.65}',
                '2025-01-01 to 2025-09-25', 'default', 
                '{"volatility": "medium", "trend": "sideways"}',
                0.75, current_time, current_time
            ),
            (
                '159740', '保守配置',
                '{"buy_drop": -0.004, "profit_target": 0.003, "stop_loss": -0.010, "signal_window": 30}',
                '{"total_return": 0.05, "sharpe_ratio": 1.5, "max_drawdown": -0.03, "win_rate": 0.70}',
                '2025-01-01 to 2025-09-25', 'genetic',
                '{"volatility": "low", "trend": "stable"}',
                0.80, current_time, current_time
            ),
            (
                '159740', '激进配置',
                '{"buy_drop": -0.008, "profit_target": 0.008, "stop_loss": -0.020, "signal_window": 15}',
                '{"total_return": 0.12, "sharpe_ratio": 0.9, "max_drawdown": -0.08, "win_rate": 0.60}',
                '2025-01-01 to 2025-09-25', 'bayesian',
                '{"volatility": "high", "trend": "volatile"}',
                0.70, current_time, current_time
            )
        ]
        
        cursor.executemany("""
            INSERT OR IGNORE INTO optimal_configs 
            (symbol, config_name, parameters, performance_metrics, backtest_period, 
             optimization_method, market_characteristics, fitness_score, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, sample_configs)
        
        print("✅ 示例优化配置插入成功")
        
    except Exception as e:
        print(f"插入示例优化配置失败: {e}")

def verify_tables():
    """验证表创建结果"""
    try:
        conn = sqlite3.connect('ticks.db')
        
        required_tables = ['optimal_configs', 'alert_rules', 'alert_history', 'market_analysis', 'optimization_tasks']
        
        for table in required_tables:
            cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"✅ 表 {table}: {count} 条记录")
        
        conn.close()
        
    except Exception as e:
        print(f"验证表失败: {e}")

if __name__ == "__main__":
    print("=== 修复数据库表缺失问题 ===")
    
    print("\n1. 检查现有表...")
    existing_tables = check_existing_tables()
    
    print("\n2. 创建缺失的表...")
    create_missing_tables()
    
    print("\n3. 验证表创建结果...")
    verify_tables()
    
    print("\n=== 修复完成 ===")
    print("现在可以正常使用实时交易面板了！")
