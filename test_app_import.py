#!/usr/bin/env python3
"""
测试应用导入是否正常
"""

import sys
import traceback

def test_imports():
    """测试各个模块的导入"""
    print("=== 测试模块导入 ===")
    
    try:
        print("1. 测试基础模块...")
        import streamlit as st
        import pandas as pd
        import numpy as np
        import sqlite3
        print("✅ 基础模块导入成功")
    except Exception as e:
        print(f"❌ 基础模块导入失败: {e}")
        return False
    
    try:
        print("2. 测试策略引擎...")
        from strategy_engine_enhanced import EnhancedStrategy, Position, RiskManager
        print("✅ 策略引擎导入成功")
    except Exception as e:
        print(f"❌ 策略引擎导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("3. 测试策略配置...")
        from strategy_config import StrategyConfig
        print("✅ 策略配置导入成功")
    except Exception as e:
        print(f"❌ 策略配置导入失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("4. 测试实时交易面板...")
        # 只导入类定义，不执行Streamlit代码
        import importlib.util
        spec = importlib.util.spec_from_file_location("app", "app_enhanced_realtime_dashboard.py")
        app_module = importlib.util.module_from_spec(spec)
        
        # 临时禁用Streamlit的执行
        import streamlit as st
        original_run = getattr(st, 'run', None)
        st.run = lambda: None
        
        spec.loader.exec_module(app_module)
        print("✅ 实时交易面板模块导入成功")
        
        # 恢复Streamlit
        if original_run:
            st.run = original_run
            
    except Exception as e:
        print(f"❌ 实时交易面板导入失败: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_database_connection():
    """测试数据库连接"""
    print("\n=== 测试数据库连接 ===")

    try:
        import sqlite3
        conn = sqlite3.connect('ticks.db')
        
        # 检查必要的表
        required_tables = ['ticks', 'optimal_configs', 'alert_rules']
        for table in required_tables:
            cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"✅ 表 {table}: {count} 条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_strategy_creation():
    """测试策略创建"""
    print("\n=== 测试策略创建 ===")
    
    try:
        from strategy_engine_enhanced import EnhancedStrategy
        strategy = EnhancedStrategy(symbol='159740')
        print(f"✅ 策略创建成功: {strategy.symbol}")
        
        # 测试参数获取
        params = strategy.current_params
        print(f"✅ 策略参数: {params}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略创建失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试应用组件...")
    
    success = True
    
    # 测试导入
    if not test_imports():
        success = False
    
    # 测试数据库
    if not test_database_connection():
        success = False
    
    # 测试策略
    if not test_strategy_creation():
        success = False
    
    print(f"\n=== 测试结果 ===")
    if success:
        print("✅ 所有测试通过，应用应该能正常启动")
        print("可以运行: streamlit run app_enhanced_realtime_dashboard.py")
    else:
        print("❌ 存在问题，需要修复后再启动应用")
